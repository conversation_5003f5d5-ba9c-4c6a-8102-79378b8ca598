'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { useRemixOnboarding, validateStep4 } from '../remix-onboarding-context';

type InitialRoles = {
  songwriter: boolean;
  toplineWriter: boolean;
  lyricist: boolean;
  melodywriter: boolean;
  beatmaker: boolean;
  arranger: boolean;
  remixer: boolean;
  vocalist: boolean;
  musician: boolean;
  engineer: boolean;
  artist: boolean;
};

type RoleKey = keyof InitialRoles;

interface RoleGroup {
  id: string;
  title: string;
  roles: Array<{
    key: RoleKey;
    label: string;
  }>;
}

const roleGroups: RoleGroup[] = [
  {
    id: 'songwriter',
    title: 'Songwriter/Composer',
    roles: [
      { key: 'songwriter', label: 'Songwriter' },
      { key: 'toplineWriter', label: 'Topline Writer' },
      { key: 'lyricist', label: 'Lyricist' },
      { key: 'melodywriter', label: 'Melodywriter' },
      { key: 'beatmaker', label: 'Beatmaker/Trackmaker' },
      { key: 'arranger', label: 'Arranger' },
      { key: 'remixer', label: 'Remixer' },
    ],
  },
  {
    id: 'musician',
    title: 'Music/Instrumentalist',
    roles: [
      { key: 'musician', label: 'Musician/Instrumentalist' },
    ],
  },
  {
    id: 'engineer',
    title: 'Engineer/Editor',
    roles: [
      { key: 'engineer', label: 'Engineer/Editor' },
    ],
  },
  {
    id: 'artist',
    title: 'Artist/Performer',
    roles: [
      { key: 'artist', label: 'Artist/Performer' },
    ],
  },
  {
    id: 'vocalist',
    title: 'Vocalist',
    roles: [
      { key: 'vocalist', label: 'Vocalist' },
    ],
  },
];

export default function Step4() {
  const { data, updateData, nextStep, prevStep } = useRemixOnboarding();
  const [roles, setRoles] = useState(data.roles);
  const [expandedGroups, setExpandedGroups] = useState<string[]>([]);

  const handleRoleChange = (roleKey: RoleKey, checked: boolean) => {
    const updatedRoles = { ...roles, [roleKey]: checked };
    setRoles(updatedRoles);
    updateData({ roles: updatedRoles });
  };

  const handleGroupSelectAll = (group: RoleGroup, checked: boolean) => {
    const updatedRoles = { ...roles };
    group.roles.forEach(role => {
      updatedRoles[role.key] = checked;
    });
    setRoles(updatedRoles);
    updateData({ roles: updatedRoles });
  };

  const handleContinue = () => {
    if (validateStep4({ ...data, roles })) {
      nextStep();
    }
  };

  const handleBack = () => {
    prevStep();
  };

  const isValid = Object.values(roles).some(role => role);

  return (
    <div className="space-y-6">
      {/* Title */}
        <h1 className="text-foreground text-3xl font-bold font-arvo leading-tight">
          Pick roles that fit you
        </h1>

      {/* Subtitle */}
        <p className="text-sm text-foreground font-arvo">
          Select roles that match your vibe (at least one required)
        </p>
     

      {/* Role Groups */}
      <Accordion
        type="multiple"
        value={expandedGroups}
        onValueChange={setExpandedGroups}
        className="space-y-4"
      >
        {roleGroups.map((group) => {
          const allRolesSelected = group.roles.every(role => roles[role.key]);

          return (
            <AccordionItem
              key={group.id}
              value={group.id}
              className="border border-border rounded-lg bg-card/60 overflow-hidden"
            >
              <AccordionTrigger className="w-full flex items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors [&[data-state=open]>svg]:rotate-180">
                <div className="flex items-center gap-3">
                  <div onClick={(e) => e.stopPropagation()}>
                    <Checkbox
                      checked={allRolesSelected}
                      onCheckedChange={(checked) => handleGroupSelectAll(group, checked as boolean)}
                      className="w-5 h-5 border-primary data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground"
                    />
                  </div>
                  <span className="text-base font-semibold text-card-foreground font-lato text-neutral-800 text-neutral-800 text-base font-semibold font-['Lato'] leading-normal">
                    {group.title}
                  </span>
                </div>
              </AccordionTrigger>

              <AccordionContent className="px-4 pb-4 space-y-1.5 mb-2">
                {group.roles.map((role) => (
                  <div key={role.key} className="flex items-center gap-3 py-1">
                    <Checkbox
                      id={role.key}
                      checked={roles[role.key]}
                      onCheckedChange={(checked) => handleRoleChange(role.key, checked as boolean)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && isValid) {
                          handleContinue();
                        }
                      }}
                      className="w-5 h-5 border-primary data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground"
                    />
                    <Label
                      htmlFor={role.key}
                      className="text-muted-foreground text-sm font-normal font-arvo leading-[16.86px] cursor-pointer"
                    >
                      {role.label}
                    </Label>
                  </div>
                ))}
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6">
        <Button
          onClick={handleBack}
          variant="outline"
          className="cursor-pointer px-6 py-3 font-arvo font-bold text-base border-muted text-muted-foreground hover:bg-muted"
        >
          Back
        </Button>
        <Button
          onClick={handleContinue}
          disabled={!isValid}
          className="cursor-pointer  px-6 py-3 bg-primary text-primary-foreground font-arvo font-bold text-base hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continue
        </Button>
      </div>
    </div>
  );
}
